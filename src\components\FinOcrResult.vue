<script setup>
import { ref, computed } from 'vue'
import { MdPreview } from 'md-editor-v3';
import 'md-editor-v3/lib/preview.css';
import "vue3-json-viewer/dist/index.css";

const props = defineProps({
  result: {
    type: Object,
    required: true
  }
})

const previewId = 'preview-only';

const text = ref(props.result.result);
console.log('OcrResult text value:', text.value);

const formattedText = computed(() => text.value || '');

// 格式化文本，确保有 ``` 标记
// const formattedText = computed(() => {
//   if (!text.value) return '';
  
//   // 如果已经包含 ``` 标记，直接返回
//   if (text.value.includes('```')) {
//     return text.value;
//   }
  
//   // 否则添加 ``` 标记
//   return `\`\`\`\n${text.value}\n\`\`\``;
// });
 
</script>

<template>
  <n-card title="" class="result-card">
        <MdPreview 
          :id="previewId" 
          :modelValue="formattedText"
          :preview-theme="'default'"
          :code-theme="'atom'"
          :codeFoldable="false"
        />
  </n-card>
</template>

<style scoped>
.result-card {
  height: 100%;
}

:deep(.n-card-header) {
  flex-shrink: 0;
}

:deep(.n-card__content) {
  height: calc(100% - 40px);
}

:deep(.n-tabs) {
  height: 100%;
}

:deep(.n-tabs-nav) {
  flex-shrink: 0;
}

:deep(.n-tab-pane) {
  height: calc(100% - 40px);
}

:deep(.md-preview) {
  height: 100%;
  overflow: auto;
}

:deep(.n-code) {
  height: 100%;
  overflow: auto;
  background-color: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
  font-family: 'Fira Code', monospace;
}
</style> 