<script setup>
import { ref } from "vue";

const emit = defineEmits(["select-type"]);
const showCards = ref(false);

const toggleCards = () => {
  showCards.value = !showCards.value;
};

const handleCardClick = (type) => {
  emit("select-type", type);
  showCards.value = false;
};
</script>

<template>
  <div class="floating-buttons">
    <button class="floating-button main-button" @click="toggleCards">
      <n-icon size="24">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
        >
          <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
          <polyline points="9 22 9 12 15 12 15 22"></polyline>
        </svg>
      </n-icon>
      <span>OCR识别</span>
    </button>

    <div class="cards-container" :class="{ show: showCards }">
      <n-card
        class="selection-card"
        hoverable
        @click="handleCardClick('injure')"
      >
        <template #cover>
          <div class="card-cover outpatient-cover">
            <n-icon size="48" class="card-icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
                <circle cx="12" cy="7" r="4"></circle>
              </svg>
            </n-icon>
          </div>
        </template>
        <h2 class="card-title">伤残鉴定报告</h2>
        <p class="card-description">Disability appraisal report</p>
      </n-card>

      <n-card
        class="selection-card"
        hoverable
        @click="handleCardClick('accident')"
      >
        <template #cover>
          <div class="card-cover hospitalized-cover">
            <n-icon size="48" class="card-icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                <polyline points="9 22 9 12 15 12 15 22"></polyline>
              </svg>
            </n-icon>
          </div>
        </template>
        <h2 class="card-title">事故责任认定书</h2>
        <p class="card-description">
          Letter of confirmation of accident responsibility
        </p>
      </n-card>

      <n-card
        class="selection-card"
        hoverable
        @click="handleCardClick('general')"
      >
        <template #cover>
          <div class="card-cover general-cover">
            <n-icon size="48" class="card-icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <rect x="4" y="2" width="16" height="20" rx="2" />
                <line x1="8" y1="6" x2="16" y2="6" />
                <line x1="8" y1="10" x2="16" y2="10" />
                <line x1="8" y1="14" x2="12" y2="14" />
              </svg>
            </n-icon>
          </div>
        </template>
        <h2 class="card-title">通用单据</h2>
        <p class="card-description">General Document</p>
      </n-card>

      <n-card
        class="selection-card"
        hoverable
        @click="handleCardClick('finance')"
      >
        <template #cover>
          <div class="card-cover finance-cover">
            <n-icon size="48" class="card-icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <rect x="3" y="3" width="18" height="18" rx="2" />
                <line x1="8" y1="9" x2="16" y2="9" />
                <line x1="8" y1="13" x2="16" y2="13" />
                <line x1="8" y1="17" x2="12" y2="17" />
              </svg>
            </n-icon>
          </div>
        </template>
        <h2 class="card-title">财报识别</h2>
        <p class="card-description">Financial Report Recognition</p>
      </n-card>

      <n-card
        class="selection-card"
        hoverable
        @click="handleCardClick('credit')"
      >
        <template #cover>
          <div class="card-cover credit-cover">
            <n-icon size="48" class="card-icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path
                  d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"
                ></path>
                <polyline points="14 2 14 8 20 8"></polyline>
                <line x1="16" y1="13" x2="8" y2="13"></line>
                <line x1="16" y1="17" x2="8" y2="17"></line>
                <polyline points="10 9 9 9 8 9"></polyline>
              </svg>
            </n-icon>
          </div>
        </template>
        <h2 class="card-title">征信报告</h2>
        <p class="card-description">Credit Report Recognition</p>
      </n-card>

      <n-card
        class="selection-card"
        hoverable
        @click="handleCardClick('fulltext')"
      >
        <template #cover>
          <div class="card-cover general-cover2">
            <n-icon size="48" class="card-icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <rect x="4" y="2" width="12" height="16" rx="2" />
                <circle cx="17" cy="17" r="3" />
                <line x1="19.2" y1="19.2" x2="21" y2="21" />
                <line x1="8" y1="6" x2="14" y2="6" />
                <line x1="8" y1="10" x2="14" y2="10" />
                <line x1="8" y1="14" x2="12" y2="14" />
              </svg>
            </n-icon>
          </div>
        </template>
        <h2 class="card-title">全文提取</h2>
        <p class="card-description">Full Text Extraction</p>
      </n-card>
    </div>
  </div>
</template>

<style scoped>
.floating-buttons {
  position: fixed;
  top: 20px;
  left: 20px;
  z-index: 1000;
}

.floating-button {
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
}

.floating-button:hover {
  background: #40a9ff;
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
}

.floating-button:active {
  transform: scale(0.95);
}

.floating-button .n-icon {
  transition: transform 0.3s ease;
}

.floating-button:hover .n-icon {
  transform: rotate(180deg);
}

.cards-container {
  position: absolute;
  top: calc(100% + 10px);
  left: 0;
  display: flex;
  gap: 10px;
  opacity: 0;
  transform: translateY(-10px);
  pointer-events: none;
  transition: all 0.3s ease;
}

.cards-container.show {
  opacity: 1;
  transform: translateY(0);
  pointer-events: all;
}

.selection-card {
  width: 200px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12px;
  overflow: hidden;
  transform: scale(0.9);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.selection-card:hover {
  transform: scale(1);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.card-cover {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #18a058 0%, #36ad6a 100%);
  transition: all 0.3s ease;
}

.hospitalized-cover {
  background: linear-gradient(135deg, #2080f0 0%, #1060c9 100%);
}

.general-cover {
  background: linear-gradient(135deg, #f5a623 0%, #f76b1c 100%);
}

.general-cover2 {
  background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
}

.finance-cover {
  background: linear-gradient(135deg, #7b4397 0%, #dc2430 100%);
}

.credit-cover {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-icon {
  color: white;
}

.card-title {
  font-size: 1.2rem;
  color: #2c3e50;
  margin: 0.5rem 0 0.2rem;
  text-align: center;
  transition: all 0.3s ease;
}

.card-description {
  color: #666;
  text-align: center;
  margin: 0;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}
</style>
