<script setup>
import { ref, computed, watch } from "vue";
import { useMessage } from "naive-ui";
import ZxOcrResult from "./ZxOcrResult.vue";

const props = defineProps({
  type: {
    type: String,
    required: true,
    validator: (value) => value === "credit",
  },
});

const message = useMessage();
const fileList = ref([]);
const loading = ref(false);
const result = ref(null);
const pageRange = ref("0");
const uploadRef = ref(null);
const isSplit = ref(false);
const isSplitComplete = ref(false);
const splitResult = ref(null); // 存储拆分后的文件信息
const showModal = ref(false);
const modalTitle = ref("");
const modalContent = ref("");
const submitCallback = ref(null);
const cancelCallback = ref(null);

const previewUrl = computed(() => {
  if (fileList.value.length === 0) return null;
  return URL.createObjectURL(fileList.value[0].file);
});

const isPdf = computed(() => {
  if (!fileList.value.length) return false;
  return fileList.value[0].file.type === "application/pdf";
});

const isImage = computed(() => {
  if (!fileList.value.length) return false;
  return fileList.value[0].file.type.startsWith("image/");
});

const canConfirm = computed(() => {
  if (!fileList.value.length) return false;
  if (isPdf.value && !pageRange.value.trim()) return false;
  return true;
});

const uploadData = computed(() => {
  if (isPdf.value) {
    return { page_range: pageRange.value };
  }
  return undefined;
});

const getApiEndpoint = (action) => {
  return "http://localhost:5003/api/credit/" + action;
};

const handleBeforeUpload = ({ file }) => {
  if (file.type !== "application/pdf" && !file.type.startsWith("image/")) {
    message.error("仅支持上传 PDF 或图片文件");
    return false;
  }
  // 不在这里设置 loading
  return true;
};

const handleFinish = ({ file, event }) => {
  try {
    const response = JSON.parse(event.target.response);
    result.value = response;
    message.success("上传成功");
  } catch (error) {
    message.error("解析响应失败：" + error.message);
  }
  loading.value = false;
};

const handleError = ({ file, event }) => {
  message.error("上传失败：" + (event?.target?.response || "未知错误"));
  loading.value = false;
};

const handleProgress = ({ file, event }) => {};

const handleRemove = () => {
  fileList.value = [];
  result.value = null;
  isSplit.value = false;
  isSplitComplete.value = false;
  splitResult.value = null;
  if (previewUrl.value) {
    URL.revokeObjectURL(previewUrl.value);
  }
};

const handleReset = () => {
  handleRemove();
  message.success("已重置");
};

const handleConfirmUpload = () => {
  if (!canConfirm.value) {
    message.error("请完善信息后再确认上传");
    return;
  }

  // 获取文件信息
  const file = fileList.value[0]?.file;
  if (!file) {
    message.error("请先选择文件");
    return;
  }

  // 构建确认信息
  const fileName = file.name;
  const fileSize = (file.size / 1024 / 1024).toFixed(2) + "MB";
  const splitStatus = isSplit.value ? "是" : "否";

  // 设置模态框内容
  modalTitle.value = "确认拆分信息";
  modalContent.value = `
    <div style="line-height: 1.6;">
      <p><strong>文件信息确认：</strong></p>
      <p>• 文件名：${fileName}</p>
      <p>• 文件大小：${fileSize}</p>
      <p>• 是否拆分：${splitStatus}</p>
      <br>
      <p>拆分只是对征信报告转换成一页一张,还没有进行OCR识别,确认要继续进行PDF拆分操作吗？</p>
    </div>
  `;

  // 设置回调函数
  submitCallback.value = () => {
    showModal.value = false;
    loading.value = true;
    message.info("正在拆分PDF文件，请稍候...");

    // 这里应该调用拆分API，拆分完成后设置 isSplitComplete.value = true
    // 暂时模拟拆分完成和服务器返回的文件信息
    setTimeout(() => {
      // 模拟服务器返回的拆分结果
      splitResult.value = {
        split_pdfs: [
          {
            filename: "page_01_left.pdf",
            url: "http://localhost:5003/uploads/original/20250725_134058/page_01_left.pdf",
            filepath: "data\\20250725_134058/page_01_left.pdf",
          },
          {
            filename: "page_01_right.pdf",
            url: "http://localhost:5003/uploads/original/20250725_134058/page_01_right.pdf",
            filepath: "data\\20250725_134058/page_01_right.pdf",
          },
          {
            filename: "page_02_left.pdf",
            url: "http://localhost:5003/uploads/original/20250725_134058/page_02_left.pdf",
            filepath: "data\\20250725_134058/page_02_left.pdf",
          },
          {
            filename: "page_02_right.pdf",
            url: "http://localhost:5003/uploads/original/20250725_134058/page_02_right.pdf",
            filepath: "data\\20250725_134058/page_02_right.pdf",
          },
        ],
        merged_pdf: {
          url: "http://localhost:5003/uploads/original/20250725_134058/new-456.pdf",
          filepath: "data\\20250725_134058\\new-456.pdf",
          filename: "new-456.pdf",
        },
        timestamp: "20250725_134058",
        data_dir: "data\\20250725_134058",
      };

      isSplitComplete.value = true;
      loading.value = false;
      message.success(
        `PDF拆分完成，共生成 ${splitResult.value.split_pdfs.length} 个文件，现在可以进行识别了`
      );
    }, 2000);
  };

  cancelCallback.value = () => {
    showModal.value = false;
    message.info("已取消拆分操作");
  };

  // 显示模态框
  showModal.value = true;
};

const handleIdentifySplitFiles = () => {
  if (!splitResult.value || !splitResult.value.split_pdfs.length) {
    message.error("没有找到拆分后的文件");
    return;
  }

  // 构建确认信息
  const mergedPdf = splitResult.value.merged_pdf;
  const oriFile = fileList.value[0]?.file;
  const fileCount = splitResult.value.split_pdfs.length;

  // 设置模态框内容
  modalTitle.value = "确认识别拆分文件";
  modalContent.value = `
    <div style="line-height: 1.6;">
      <p><strong>拆分文件识别确认：</strong></p>
      <p>• 原始PDF：${oriFile.name}</p>
      <p>• 拆分文件数：${fileCount} 个</p>
      <p>• 合并后PDF：${mergedPdf.filename}</p>
      <br>
      <p>本次识别是对新文件（${mergedPdf.filename}）进行OCR识别，是否继续？</p>
    </div>
  `;

  // 设置回调函数
  submitCallback.value = () => {
    showModal.value = false;
    loading.value = true;
    message.info("正在对拆分文件进行OCR识别，请稍候...");

    // 准备传递给服务器的参数
    const apiParams = {
      merged_pdf: {
        filename: splitResult.value.merged_pdf.filename,
        url: splitResult.value.merged_pdf.url,
        filepath: splitResult.value.merged_pdf.filepath,
      },
      split_pdfs: splitResult.value.split_pdfs.map((file) => ({
        filename: file.filename,
        url: file.url,
        filepath: file.filepath,
      })),
      timestamp: splitResult.value.timestamp,
      data_dir: splitResult.value.data_dir,
      action: "identify_split_files",
    };

    console.log("调用识别API，参数：", apiParams);

    // 实际API调用示例（目前注释掉，使用模拟）
    /*
    fetch(getApiEndpoint('identify_split'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(apiParams)
    })
    .then(response => response.json())
    .then(data => {
      loading.value = false;
      result.value = data;
      message.success("拆分文件OCR识别完成");
    })
    .catch(error => {
      loading.value = false;
      message.error("识别失败：" + error.message);
    });
    */

    // 暂时模拟识别完成
    setTimeout(() => {
      loading.value = false;
      // 模拟识别结果
      result.value = {
        success: true,
        message: "识别完成",
        data: {
          merged_pdf: splitResult.value.merged_pdf.filename,
          total_files: splitResult.value.split_pdfs.length,
          timestamp: splitResult.value.timestamp,
          results: splitResult.value.split_pdfs.map((file, index) => ({
            index: index + 1,
            filename: file.filename,
            content: `${file.filename} 识别结果内容...`,
          })),
        },
      };
      message.success("拆分文件OCR识别完成");
    }, 3000);
  };

  cancelCallback.value = () => {
    showModal.value = false;
    message.info("已取消识别操作");
  };

  // 显示模态框
  showModal.value = true;
};

const handleIdentifySplitFile = (file) => {
  if (!file || !file.url) {
    message.error("文件信息不完整");
    return;
  }

  // 设置模态框内容
  modalTitle.value = "确认识别单个文件";
  modalContent.value = `
    <div style="line-height: 1.6;">
      <p><strong>单个文件识别确认：</strong></p>
      <p>• 识别文件：${file.filename}</p>
      <br>
      <p>确认要对这个文件进行OCR识别吗？</p>
    </div>
  `;

  // 设置回调函数
  submitCallback.value = () => {
    showModal.value = false;
    loading.value = true;
    message.info(`正在识别 ${file.filename}，请稍候...`);

    // 准备传递给服务器的参数
    const apiParams = {
      split_pdf: {
        filename: file.filename,
        url: file.url,
        filepath: file.filepath,
      },
      merged_pdf: {
        filename: splitResult.value.merged_pdf.filename,
        url: splitResult.value.merged_pdf.url,
        filepath: splitResult.value.merged_pdf.filepath,
      },
      timestamp: splitResult.value.timestamp,
      data_dir: splitResult.value.data_dir,
      action: "identify_single_file",
    };

    console.log("调用单个文件识别API，参数：", apiParams);

    // 实际API调用示例（目前注释掉，使用模拟）
    /*
    fetch(getApiEndpoint('identify_single'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(apiParams)
    })
    .then(response => response.json())
    .then(data => {
      loading.value = false;
      result.value = data;
      message.success(`第${pageNumber}页识别完成`);
    })
    .catch(error => {
      loading.value = false;
      message.error("识别失败：" + error.message);
    });
    */

    // 暂时模拟识别完成
    setTimeout(() => {
      loading.value = false;
      // 模拟识别结果
      result.value = {
        success: true,
        message: "单个文件识别完成",
        data: {
          merged_pdf: splitResult.value.merged_pdf.filename,
          split_pdf: file.filename,
          timestamp: splitResult.value.timestamp,
          content: `${file.filename} 识别结果内容...`,
        },
      };
      message.success(`${file.filename} OCR识别完成`);
    }, 2000);
  };

  cancelCallback.value = () => {
    showModal.value = false;
    message.info("已取消识别操作");
  };

  // 显示模态框
  showModal.value = true;
};

const handlePreviewSplitPdf = () => {
  if (!splitResult.value || !splitResult.value.merged_pdf) {
    message.error("没有找到合并后的PDF文件");
    return;
  }

  // 在新窗口中打开PDF文件
  window.open(splitResult.value.merged_pdf.url, "_blank");
};

const handleIdentify = () => {
  if (!canConfirm.value) {
    message.error("请完善信息后再进行识别");
    return;
  }

  // 获取文件信息
  const file = fileList.value[0]?.file;
  if (!file) {
    message.error("请先选择文件");
    return;
  }

  // 构建确认信息
  const fileName = file.name;
  const fileSize = (file.size / 1024 / 1024).toFixed(2) + "MB";
  const fileType = isPdf.value ? "PDF文件" : "图片文件";
  const splitStatus = isPdf.value
    ? isSplit.value
      ? "已拆分"
      : "未拆分"
    : "不适用";
  const pageInfo = isPdf.value ? pageRange.value || "1" : "不适用";

  // 设置模态框内容
  modalTitle.value = "确认识别信息";
  modalContent.value = `
    <div style="line-height: 1.6;">
      <p><strong>识别信息确认：</strong></p>
      <p>• 文件名：${fileName}</p>
      <p>• 文件类型：${fileType}</p>
      <p>• 文件大小：${fileSize}</p>
      <p>• 拆分状态：${splitStatus}</p>
      <p>• 识别页码：${pageInfo === "0" ? "全部" : pageInfo}</p>
      <br>
      <p>确认要开始进行OCR识别吗？</p>
    </div>
  `;

  // 设置回调函数
  submitCallback.value = () => {
    showModal.value = false;
    loading.value = true;
    message.info("正在进行OCR识别，请稍候...");
    uploadRef.value.submit();
  };

  cancelCallback.value = () => {
    showModal.value = false;
    message.info("已取消识别操作");
  };

  // 显示模态框
  showModal.value = true;
};

defineExpose({
  reset: handleReset,
});

const openInNewTab = (url = null) => {
  const targetUrl = url || previewUrl.value;
  if (targetUrl) {
    window.open(targetUrl, "_blank");
  } else {
    message.warning("没有可预览的文件");
  }
};
</script>

<template>
  <div class="ocr-container">
    <div class="left-panel">
      <n-tabs type="line" animated>
        <n-tab-pane name="gr" tab="个人征信报告">
          <n-card title=" " class="upload-card">
            <template #header-extra>
              <n-button
                v-if="fileList.length > 0 || result"
                type="warning"
                size="small"
                @click="handleReset"
              >
                <template #icon>
                  <n-icon>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path
                        d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"
                      ></path>
                      <path d="M3 3v5h5"></path>
                    </svg>
                  </n-icon>
                </template>
                重置
              </n-button>
            </template>
            <n-upload
              ref="uploadRef"
              v-model:file-list="fileList"
              :max="1"
              :action="getApiEndpoint()"
              :data="uploadData"
              :default-upload="false"
              :on-before-upload="handleBeforeUpload"
              :on-finish="handleFinish"
              :on-error="handleError"
              :on-progress="handleProgress"
              :on-remove="handleRemove"
              accept=".pdf,image/*"
              :show-file-list="true"
            >
              <n-upload-dragger>
                <div class="upload-trigger">
                  <n-icon size="48" depth="3">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path
                        d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"
                      ></path>
                      <polyline points="17 8 12 3 7 8"></polyline>
                      <line x1="12" y1="3" x2="12" y2="15"></line>
                    </svg>
                  </n-icon>
                  <n-text style="margin-top: 8px">
                    点击或拖拽文件到该区域来上传
                  </n-text>
                  <n-text depth="3" style="font-size: 12px; margin-top: 8px">
                    支持 pdf、图片格式
                  </n-text>
                </div>
              </n-upload-dragger>
            </n-upload>

            <n-space justify="space-between" style="margin-top: 10px">
              <!-- PDF文件显示拆分开关 -->
              <n-space v-if="isPdf && fileList.length">
                <n-tooltip trigger="hover">
                  <template #trigger>
                    <n-space>
                      <n-text>拆分PDF：</n-text>
                      <n-switch v-model:value="isSplit" />
                    </n-space>
                  </template>
                  如果征信报告PDF非一页一张,请开启拆分
                </n-tooltip>
              </n-space>

              <!-- 页码输入框：PDF文件且(选择拆分已完成 或 不拆分)时显示 -->
              <n-tooltip
                trigger="hover"
                v-if="isPdf && fileList.length && !isSplit"
              >
                <template #trigger>
                  <n-form-item label="识别页数:" label-placement="left">
                    <n-input
                      v-model:value="pageRange"
                      placeholder="请输入需要识别的页码，如 1,3,5-7 （0代表全部）"
                    />
                  </n-form-item>
                </template>
                输入识别页数,0代表全部
              </n-tooltip>

              <!-- 确认上传按钮：PDF文件且选择拆分且未完成拆分时显示 -->
              <n-button
                v-if="isPdf && fileList.length && isSplit && !isSplitComplete"
                type="primary"
                :disabled="!fileList.length"
                @click="handleConfirmUpload"
              >
                确认上传
              </n-button>

              <!-- 直接识别按钮：图片文件 或 PDF文件且不拆分时显示 -->
              <n-button
                v-if="
                  (isImage && fileList.length) ||
                  (isPdf && fileList.length && !isSplit)
                "
                type="primary"
                :disabled="!canConfirm"
                @click="handleIdentify"
              >
                直接识别
              </n-button>
            </n-space>
          </n-card>

          <!-- 拆分结果显示卡片 -->
          <n-card
            v-if="splitResult && isSplitComplete"
            title="拆分结果"
            class="split-result-card"
          >
            <template #header-extra>
              <!-- 拆分文件识别按钮：PDF文件且拆分完成时显示 -->

              <n-space
                v-if="
                  isPdf && fileList.length && isSplitComplete && splitResult
                "
              >
                <n-tooltip trigger="hover">
                  <template #trigger>
                    <n-button @click="handleIdentifySplitFiles">
                      识别PDF
                    </n-button>
                  </template>
                  对拆分后的文件 进行全部识别
                </n-tooltip>

                <n-tooltip trigger="hover">
                  <template #trigger>
                    <n-button @click="handlePreviewSplitPdf">
                      预览PDF
                    </n-button>
                  </template>
                  预览拆分后的PDF
                </n-tooltip>
              </n-space>
            </template>

            <n-space vertical>
              <n-text>
                <n-text type="success">✓</n-text>
                PDF拆分完成，共生成 {{ splitResult.split_pdfs.length }} 个文件
              </n-text>
              <n-list>
                <n-list-item
                  v-for="(file, index) in splitResult.split_pdfs"
                  :key="index"
                >
                  <n-space justify="space-between">
                    <n-space>
                      <n-text code>{{ index + 1 }}</n-text>
                      <n-text code>{{ file.filename }}</n-text>
                    </n-space>
                    <n-space>
                      <n-button
                        size="small"
                        type="primary"
                        text
                        @click="openInNewTab(file.url)"
                      >
                        预览
                      </n-button>
                      <n-button
                        size="small"
                        type="primary"
                        text
                        @click="handleIdentifySplitFile(file)"
                      >
                        识别
                      </n-button>
                    </n-space>
                  </n-space>
                </n-list-item>
              </n-list>
            </n-space>
          </n-card>

          <n-card v-if="previewUrl" title="原文件预览" class="preview-card">
            <template #header-extra>
              <n-space>
                <n-button v-if="isPdf" size="small" @click="openInNewTab">
                  <template #icon>
                    <n-icon>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                      >
                        <path
                          d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"
                        ></path>
                        <polyline points="15 3 21 3 21 9"></polyline>
                        <line x1="10" y1="14" x2="21" y2="3"></line>
                      </svg>
                    </n-icon>
                  </template>
                  新标签页查看
                </n-button>
              </n-space>
            </template>
            <div class="preview-container">
              <n-image v-if="!isPdf" :src="previewUrl" width="400" />
              <div v-else class="pdf-container">
                <iframe :src="previewUrl" class="preview-pdf"></iframe>
                <div class="pdf-overlay">
                  <n-button type="primary" size="large" @click="openInNewTab">
                    <template #icon>
                      <n-icon>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="2"
                        >
                          <path d="M15 3h6v6"></path>
                          <path d="M10 14L21 3"></path>
                          <path
                            d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"
                          ></path>
                        </svg>
                      </n-icon>
                    </template>
                    在新标签页中查看
                  </n-button>
                </div>
              </div>
            </div>
          </n-card>
        </n-tab-pane>
        <n-tab-pane name="qy" tab="企业征信报告">
          <n-alert title="提示" type="warning"> 该功能暂未开放 </n-alert>
        </n-tab-pane>
      </n-tabs>
    </div>

    <div class="right-panel">
      <n-spin :show="loading">
        <template v-if="result">
          <ZxOcrResult :result="result" />
        </template>
        <template v-else>
          <n-card title="" class="result-card">
            <div class="empty-result">
              <n-empty description="请上传文件进行识别" />
            </div>
          </n-card>
        </template>
        <template #description>
          正在处理中，请稍等（当前为测试环境,比较缓慢，请耐心等待）
        </template>
      </n-spin>
    </div>

    <!-- 确认模态框 -->
    <n-modal
      v-model:show="showModal"
      preset="dialog"
      :title="modalTitle"
      positive-text="确认"
      negative-text="取消"
      @positive-click="submitCallback"
      @negative-click="cancelCallback"
    >
      <div v-html="modalContent"></div>
    </n-modal>
  </div>
</template>

<style scoped>
.ocr-container {
  display: flex;
  gap: 20px;
  padding: 20px;
  box-sizing: border-box;
}

.left-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-width: 400px;
  height: 100%;
}

.right-panel {
  flex: 1;
  min-width: 400px;
  height: 100%;
}

.upload-card {
  flex-shrink: 0;
  min-height: 120px;
}

.preview-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 500px;
  margin-top: 5px;
}

:deep(.preview-card .n-card-header) {
  flex-shrink: 0;
}

:deep(.preview-card .n-card__content) {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.preview-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: auto;
  min-height: 0;
  padding: 20px;
}

:deep(.n-image) {
  max-width: 100%;
  max-height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

:deep(.n-image img) {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

:deep(.n-image-preview-container) {
  max-width: 100%;
  max-height: 100%;
}

:deep(.n-image-preview) {
  max-width: 100%;
  max-height: 100%;
}

.pdf-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 0;
}

.pdf-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.pdf-container:hover .pdf-overlay {
  opacity: 1;
}

.preview-pdf {
  width: 100%;
  height: 100%;
  border: none;
  background: white;
}

.result-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

:deep(.result-card .n-card-header) {
  flex-shrink: 0;
}

:deep(.result-card .n-card__content) {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.upload-trigger {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
}

.empty-result {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.split-result-card {
  margin-top: 10px;
  margin-bottom: 10px;
}

.split-result-card :deep(.n-list-item) {
  padding: 8px 0;
}
</style>
