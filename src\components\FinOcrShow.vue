<script setup>
import { ref, computed, watch } from 'vue'
import { useMessage } from 'naive-ui'
import FinOcrResult from './FinOcrResult.vue'

const props = defineProps({
  type: {
    type: String,
    required: true,
    validator: (value) => value === 'finance'
  }
})

const message = useMessage()
const fileList = ref([])
const loading = ref(false)
const result = ref(null)
const pageRange = ref('')
const uploadRef = ref(null)

const previewUrl = computed(() => {
  if (fileList.value.length === 0) return null
  return URL.createObjectURL(fileList.value[0].file)
})

const isPdf = computed(() => {
  if (!fileList.value.length) return false
  return fileList.value[0].file.type === 'application/pdf'
})

const isImage = computed(() => {
  if (!fileList.value.length) return false
  return fileList.value[0].file.type.startsWith('image/')
})

const canConfirm = computed(() => {
  if (!fileList.value.length) return false
  if (isPdf.value && !pageRange.value.trim()) return false
  return true
})

const uploadData = computed(() => {
  if (isPdf.value) {
    return { page_range: pageRange.value }
  }
  return undefined
})

const getApiEndpoint = () => {
  return 'http://localhost:5001/api/finance/upload'
}

const getTitle = () => {
  return '财报识别'
}

const handleBeforeUpload = ({ file }) => {
  if (file.type !== 'application/pdf' && !file.type.startsWith('image/')) {
    message.error('仅支持上传 PDF 或图片文件')
    return false
  }
  // 不在这里设置 loading
  return true
}

const handleFinish = ({ file, event }) => {
  try {
    const response = JSON.parse(event.target.response)
    result.value = response
    message.success('上传成功')
  } catch (error) {
    message.error('解析响应失败：' + error.message)
  }
  loading.value = false
}

const handleError = ({ file, event }) => {
  message.error('上传失败：' + (event?.target?.response || '未知错误'))
  loading.value = false
}

const handleProgress = ({ file, event }) => {}

const handleRemove = () => {
  fileList.value = []
  result.value = null
  if (previewUrl.value) {
    URL.revokeObjectURL(previewUrl.value)
  }
}

const handleReset = () => {
  handleRemove()
  message.success('已重置')
}

const handleConfirmUpload = () => {
  if (!canConfirm.value) {
    message.error('请完善信息后再确认上传')
    return
  }
  loading.value = true
  uploadRef.value.submit()
}

defineExpose({
  reset: handleReset
})

const openInNewTab = () => {
  if (previewUrl.value) {
    window.open(previewUrl.value, '_blank')
  }
}
</script>

<template>
  <div class="ocr-container">
    <div class="left-panel">
      <n-card :title="getTitle()" class="upload-card">
        <template #header-extra>
          <n-button
            v-if="fileList.length > 0 || result"
            type="warning"
            size="small"
            @click="handleReset"
          >
            <template #icon>
              <n-icon>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path>
                  <path d="M3 3v5h5"></path>
                </svg>
              </n-icon>
            </template>
            重置
          </n-button>
        </template>
        <n-upload
          ref="uploadRef"
          v-model:file-list="fileList"
          :max="1"
          :action="getApiEndpoint()"
          :data="uploadData"
          :default-upload="false"
          :on-before-upload="handleBeforeUpload"
          :on-finish="handleFinish"
          :on-error="handleError"
          :on-progress="handleProgress"
          :on-remove="handleRemove"
          accept=".pdf,image/*"
          :show-file-list="true"
        >
          <n-upload-dragger>
            <div class="upload-trigger">
              <n-icon size="48" depth="3">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                  <polyline points="17 8 12 3 7 8"></polyline>
                  <line x1="12" y1="3" x2="12" y2="15"></line>
                </svg>
              </n-icon>
              <n-text style="margin-top: 8px">
                点击或拖拽文件到该区域来上传
              </n-text>
              <n-text depth="3" style="font-size: 12px; margin-top: 8px">
                支持 pdf、图片格式
              </n-text>
            </div>
          </n-upload-dragger>
        </n-upload>
        <n-input
          v-if="isPdf && fileList.length"
          v-model:value="pageRange"
          placeholder="请输入需要识别的页码，如 1,3,5-7"
          style="margin: 16px 0 0 0; width: 100%;"
        />
        <n-button
          type="primary"
          style="margin-top: 16px; width: 100%;"
          :disabled="!canConfirm"
          @click="handleConfirmUpload"
        >
          确认上传
        </n-button>
      </n-card>

      <n-card v-if="previewUrl" title="文件预览" class="preview-card">
        <template #header-extra>
          <n-space>
            <n-button
              v-if="isPdf"
              size="small"
              @click="openInNewTab"
            >
              <template #icon>
                <n-icon>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                    <polyline points="15 3 21 3 21 9"></polyline>
                    <line x1="10" y1="14" x2="21" y2="3"></line>
                  </svg>
                </n-icon>
              </template>
              新标签页查看
            </n-button>
          </n-space>
        </template>
        <div class="preview-container">
          <n-image v-if="!isPdf" :src="previewUrl" width="400" />
          <div v-else class="pdf-container">
            <iframe :src="previewUrl" class="preview-pdf"></iframe>
            <div class="pdf-overlay">
              <n-button
                type="primary"
                size="large"
                @click="openInNewTab"
              >
                <template #icon>
                  <n-icon>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <path d="M15 3h6v6"></path>
                      <path d="M10 14L21 3"></path>
                      <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                    </svg>
                  </n-icon>
                </template>
                在新标签页中查看
              </n-button>
            </div>
          </div>
        </div>
      </n-card>
    </div>

    <div class="right-panel">
      <n-spin :show="loading">
        <template v-if="result">
          <FinOcrResult :result="result"  />
        </template>
        <template v-else>
          <n-card title="" class="result-card">
            <div class="empty-result">
              <n-empty description="请上传文件进行识别" />
            </div>
          </n-card>
        </template>
        <template #description>
        正在识别中，请稍等（当前为测试环境,比较缓慢，请耐心等待）
      </template>
      </n-spin>
    </div>
  </div>
</template>

<style scoped>
.ocr-container {
  display: flex;
  gap: 20px;
  padding: 20px;
  box-sizing: border-box;
}

.left-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-width: 400px;
  height: 100%;
}

.right-panel {
  flex: 1;
  min-width: 400px;
  height: 100%;
}

.upload-card {
  flex-shrink: 0;
  min-height: 120px;
}

.preview-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 500px;
}

:deep(.preview-card .n-card-header) {
  flex-shrink: 0;
}

:deep(.preview-card .n-card__content) {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.preview-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: auto;
  min-height: 0;
  padding: 20px;
}

:deep(.n-image) {
  max-width: 100%;
  max-height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

:deep(.n-image img) {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

:deep(.n-image-preview-container) {
  max-width: 100%;
  max-height: 100%;
}

:deep(.n-image-preview) {
  max-width: 100%;
  max-height: 100%;
}

.pdf-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 0;
}

.pdf-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.pdf-container:hover .pdf-overlay {
  opacity: 1;
}

.preview-pdf {
  width: 100%;
  height: 100%;
  border: none;
  background: white;
}

.result-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

:deep(.result-card .n-card-header) {
  flex-shrink: 0;
}

:deep(.result-card .n-card__content) {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.upload-trigger {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
}

.empty-result {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style> 