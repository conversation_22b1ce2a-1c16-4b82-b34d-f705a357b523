<script setup>
import { ref, computed } from 'vue'
import { MdPreview } from 'md-editor-v3';
import 'md-editor-v3/lib/preview.css';
import "vue3-json-viewer/dist/index.css";

const props = defineProps({
  result: {
    type: Object,
    required: true
  }
})

const previewId = 'preview-fulltext';

const text = ref(props.result.result);

// 去除 markdown 前后的 ```
const plainText = computed(() => {
  if (!text.value) return '';
  // 去除前后的 ``` 和换行
  return text.value.replace(/^```[a-zA-Z]*\n?|\n?```$/g, '').trim();
});

const formattedText = computed(() => text.value || '');

// 下载原始 markdown 文件
function downloadMarkdown() {
  const blob = new Blob([text.value], { type: 'text/markdown' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = 'fulltext.md'
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

// 下载 Word 文件
async function downloadWord() {
  try {
    const res = await fetch('http://localhost:5001/api/fulltext/word', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ content: plainText.value })
    })
    if (!res.ok) throw new Error('下载失败')
    const blob = await res.blob()
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'fulltext.docx'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  } catch (e) {
    message.error('下载Word文件失败' )
  }
}
</script>

<template>
  <n-card title="" class="result-card">
    <template #header>
      <n-space>
        <n-button size="small" @click="downloadMarkdown">下载原始文件</n-button>
        <n-button size="small" type="primary" @click="downloadWord">下载Word文件</n-button>
      </n-space>
    </template>
        <MdPreview 
          :id="previewId" 
          :modelValue="formattedText"
          :preview-theme="'default'"
          :code-theme="'atom'"
          :codeFoldable="false"
        />
  </n-card>
</template>

<style scoped>
.result-card {
  height: 100%;
}

:deep(.n-card-header) {
  flex-shrink: 0;
}

:deep(.n-card__content) {
  height: calc(100% - 40px);
}

:deep(.n-tabs) {
  height: 100%;
}

:deep(.n-tabs-nav) {
  flex-shrink: 0;
}

:deep(.n-tab-pane) {
  height: calc(100% - 40px);
}

:deep(.md-preview) {
  height: 100%;
  overflow: auto;
}

:deep(.n-code) {
  height: 100%;
  overflow: auto;
  background-color: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
  font-family: 'Fira Code', monospace;
}
</style> 