<script setup>
import { computed } from 'vue'

const props = defineProps({
  data: {
    type: Object,
    required: true
  }
})

// 获取文档类型
const documentType = computed(() => props.data.document_type || '')
 
 
</script>

<template>
  <div class="form-container">
    <template v-if="isUnknownType">
      <n-alert type="warning" title="未知文档类型">
        当前文档类型 "{{ documentType }}" 未知，暂时不能进行表单方式查看。
        请使用 JSON 数据或原始结果查看。
      </n-alert>
    </template>
    <template v-else>
      <!-- 基本信息 -->
      <n-descriptions label-placement="top" bordered>
        <template v-for="(value, key) in filteredData" :key="key">
          <template v-if="!isComplexValue(value)">
            <n-descriptions-item :label="getFieldLabel(key)">
              {{ value }}
            </n-descriptions-item>
          </template>
        </template>
      </n-descriptions>

      <!-- 复杂数据（数组或对象） -->
      <template v-for="(value, key) in filteredData" :key="key">
        <template v-if="isComplexValue(value)">
          <div class="complex-section">
            <h3>明细:</h3>
            
            <!-- 数组类型 -->
            <template v-if="Array.isArray(value)">
              <div v-for="(item, index) in value" :key="index" class="array-item">
                <n-descriptions label-placement="top" bordered>
                  <template v-for="(nestedValue, nestedKey) in item" :key="nestedKey">
                    <n-descriptions-item :label="getFieldLabel(key, nestedKey)">
                      {{ nestedValue }}
                    </n-descriptions-item>
                  </template>
                </n-descriptions>
              </div>
            </template>

            <!-- 对象类型 -->
            <template v-else>
              <n-descriptions label-placement="top" bordered>
                <template v-for="(nestedValue, nestedKey) in value" :key="nestedKey">
                  <n-descriptions-item :label="getFieldLabel(key, nestedKey)">
                    {{ nestedValue }}
                  </n-descriptions-item>
                </template>
              </n-descriptions>
            </template>
          </div>
        </template>
      </template>
    </template>
  </div>
</template>

<style scoped>
.form-container {
  padding: 16px;
}

.n-descriptions {
  margin-bottom: 24px;
}

.complex-section {
  margin-top: 24px;
}

.complex-section h3 {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.array-item {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.array-item:last-child {
  margin-bottom: 0;
}
</style> 