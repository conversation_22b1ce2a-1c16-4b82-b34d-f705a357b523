<script setup>
import { ref, computed } from 'vue'
import { MdPreview } from 'md-editor-v3';
import 'md-editor-v3/lib/preview.css';
import "vue3-json-viewer/dist/index.css";
import OcrForm from './OcrForm.vue'

const props = defineProps({
  result: {
    type: Object,
    required: true
  },
  type: {
    type: String,
    required: true,
    validator: (value) => ['accident', 'injure', 'general'].includes(value)
  }
})

const activeTab = ref('formatted')
const previewId = 'preview-only';

const text = ref(props.result.result);
console.log('OcrResult text value:', text.value);

// 格式化文本，确保有 ``` 标记
const formattedText = computed(() => {
  if (!text.value) return '';
  
  // 如果已经包含 ``` 标记，直接返回
  if (text.value.includes('```')) {
    return text.value;
  }
  
  // 否则添加 ``` 标记
  return `\`\`\`\n${text.value}\n\`\`\``;
});

// 提取 JSON 数据
const jsonData = computed(() => {
  if (!text.value) return null;
  
  try {
    // 如果包含 ```json，替换掉标记
    const jsonText = text.value.includes('```json') 
      ? text.value.replace('```json\n', '').replace('```', '')
      : text.value;
    
    return JSON.parse(jsonText);
  } catch (e) {
    console.error('JSON parsing error:', e);
    return null;
  }
});
</script>

<template>
  <n-card title="识别结果" class="result-card">
    <n-tabs v-model:value="activeTab" type="line" animated>
      <n-tab-pane name="table" tab="表格展示">
        <n-table :bordered="true" :single-line="false" style="width:100%">
          <thead>
            <tr>
              <th style="width: 180px;">字段</th>
              <th>内容</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(value, key) in jsonData" :key="key">
              <td>{{ key }}</td>
              <td style="white-space: pre-line;">{{ value }}</td>
            </tr>
          </tbody>
        </n-table>
      </n-tab-pane>

      <n-tab-pane name="formatted" tab="原始结果">
        <MdPreview 
          :id="previewId" 
          :modelValue="formattedText"
          :preview-theme="'default'"
          :code-theme="'atom'"
          :codeFoldable="false"
        />
      </n-tab-pane>
      <n-tab-pane name="json" tab="JSON数据">
        <json-viewer
          :value="jsonData"
          :expand-depth="5"
          copyable
          boxed
          sort
          expanded
        ></json-viewer>
      </n-tab-pane>

    </n-tabs>
  </n-card>
</template>

<style scoped>
.result-card {
  height: 100%;
}

:deep(.n-card-header) {
  flex-shrink: 0;
}

:deep(.n-card__content) {
  height: calc(100% - 40px);
}

:deep(.n-tabs) {
  height: 100%;
}

:deep(.n-tabs-nav) {
  flex-shrink: 0;
}

:deep(.n-tab-pane) {
  height: calc(100% - 40px);
}

:deep(.md-preview) {
  height: 100%;
  overflow: auto;
}

:deep(.n-code) {
  height: 100%;
  overflow: auto;
  background-color: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
  font-family: 'Fira Code', monospace;
}
</style> 