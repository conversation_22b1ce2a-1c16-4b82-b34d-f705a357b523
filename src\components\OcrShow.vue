<script setup>
import { ref, computed, watch } from 'vue'
import { useMessage } from 'naive-ui'
import OcrResult from './OcrResult.vue'

const props = defineProps({
  type: {
    type: String,
    required: true,
    validator: (value) => ['outpatient', 'hospitalized'].includes(value)
  }
})

const message = useMessage()
const fileList = ref([])
const loading = ref(false)
const result = ref(null)
const extractContent = ref('')

const previewUrl = computed(() => {
  if (fileList.value.length === 0) return null
  return URL.createObjectURL(fileList.value[0].file)
})

const isPdf = computed(() => {
  if (!fileList.value.length) return false
  return fileList.value[0].file.type === 'application/pdf'
})

const uploadData = computed(() => {
  if (props.type === 'general') {
    return { extract_content: extractContent.value }
  }
  return undefined
})

const getApiEndpoint = () => {
  return 'http://localhost:5001/api/'+props.type +'/upload'
}

const getTitle = () => {
  if (props.type === 'accident') return '事故责任认定书识别'
  if (props.type === 'injure') return '伤残鉴定报告识别'
  if (props.type === 'general') return '通用单据识别'
  return '单据识别'
}

const handleBeforeUpload = ({ file }) => {

  if (props.type === 'general' && !extractContent.value.trim()) {
    message.error('请先填写要提取的内容');
    return false;
  }

  if (file.type !== 'application/pdf') {
    message.error('仅支持上传 PDF 文件')
    return false
  }
  console.log('开始上传文件:', file.name)
  loading.value = true
  return true
}

const handleFinish = ({ file, event }) => {
  console.log('上传完成:', file.name)
  try {
    const response = JSON.parse(event.target.response)
    console.log('上传成功，返回数据:', response)
    result.value = response
    message.success('上传成功')
  } catch (error) {
    console.error('解析响应失败:', error)
    message.error('解析响应失败：' + error.message)
  }
  loading.value = false
}

const handleError = ({ file, event }) => {
  console.error('上传失败:', file.name, event)
  message.error('上传失败：' + (event?.target?.response || '未知错误'))
  loading.value = false
}

const handleProgress = ({ file, event }) => {
  console.log('上传进度:', file.name, event)
}

const handleRemove = () => {
  fileList.value = []
  result.value = null
  if (previewUrl.value) {
    URL.revokeObjectURL(previewUrl.value)
  }
}

const handleReset = () => {
  handleRemove()
  message.success('已重置')
}

// 暴露重置方法给父组件
defineExpose({
  reset: handleReset
})

const openInNewTab = () => {
  if (previewUrl.value) {
    window.open(previewUrl.value, '_blank')
  }
}

// watch(extractContent, (newVal) => {
//   console.log('extractContent changed:', newVal)
// })
</script>

<template>
  <div class="ocr-container">
    <div class="left-panel">
      <n-card :title="getTitle()" class="upload-card">
        <template #header-extra>
          <n-button
            v-if="fileList.length > 0 || result"
            type="warning"
            size="small"
            @click="handleReset"
          >
            <template #icon>
              <n-icon>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path>
                  <path d="M3 3v5h5"></path>
                </svg>
              </n-icon>
            </template>
            重置
          </n-button>
        </template>
        <n-upload
          v-model:file-list="fileList"
          :max="1"
          :action="getApiEndpoint()"
          :data="uploadData"
          :on-before-upload="handleBeforeUpload"
          :on-finish="handleFinish"
          :on-error="handleError"
          :on-progress="handleProgress"
          :on-remove="handleRemove"
          accept=".pdf"
          :show-file-list="true"
        >
          <n-upload-dragger>
            <div class="upload-trigger">
              <n-icon size="48" depth="3">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                  <polyline points="17 8 12 3 7 8"></polyline>
                  <line x1="12" y1="3" x2="12" y2="15"></line>
                </svg>
              </n-icon>
              <n-text style="margin-top: 8px">
                点击或拖拽文件到该区域来上传
              </n-text>
              <n-text depth="3" style="font-size: 12px; margin-top: 8px">
                支持 pdf 格式
              </n-text>
            </div>
          </n-upload-dragger>
        </n-upload>
      </n-card>

      <n-input
        v-if="props.type === 'general'"
        v-model:value="extractContent"   type="textarea"
        placeholder="请输入要提取的关键内容,用逗号分割。如: aaa,bbb,ccc,ddd"        
        clearable
        style="margin: 16px 0 0 0; width: 100%;"
      />

      <n-card v-if="previewUrl" title="文件预览" class="preview-card">
        <template #header-extra>
          <n-space>
            <n-button
              v-if="isPdf"
              size="small"
              @click="openInNewTab"
            >
              <template #icon>
                <n-icon>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                    <polyline points="15 3 21 3 21 9"></polyline>
                    <line x1="10" y1="14" x2="21" y2="3"></line>
                  </svg>
                </n-icon>
              </template>
              新标签页查看
            </n-button>
          </n-space>
        </template>
        <div class="preview-container">
          <n-image v-if="!isPdf" :src="previewUrl" width="400" />
          <div v-else class="pdf-container">
            <iframe :src="previewUrl" class="preview-pdf"></iframe>
            <div class="pdf-overlay">
              <n-button
                type="primary"
                size="large"
                @click="openInNewTab"
              >
                <template #icon>
                  <n-icon>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <path d="M15 3h6v6"></path>
                      <path d="M10 14L21 3"></path>
                      <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                    </svg>
                  </n-icon>
                </template>
                在新标签页中查看
              </n-button>
            </div>
          </div>
        </div>
      </n-card>
    </div>

    <div class="right-panel">
      <n-spin :show="loading">
        <template v-if="result">
          <OcrResult :result="result" :type="type" />
        </template>
        <template v-else>
          <n-card title="识别结果" class="result-card">
            <div class="empty-result">
              <n-empty description="请上传文件进行识别" />
            </div>
          </n-card>
        </template>
      </n-spin>
    </div>
  </div>
</template>

<style scoped>
.ocr-container {
  display: flex;
  gap: 20px;
  padding: 20px;
  box-sizing: border-box;
}

.left-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-width: 400px;
  height: 100%;
}

.right-panel {
  flex: 1;
  min-width: 400px;
  height: 100%;
}

.upload-card {
  flex-shrink: 0;
  min-height: 120px;
}

.preview-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 500px;
}

:deep(.preview-card .n-card-header) {
  flex-shrink: 0;
}

:deep(.preview-card .n-card__content) {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.preview-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: auto;
  min-height: 0;
  padding: 20px;
}

:deep(.n-image) {
  max-width: 100%;
  max-height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

:deep(.n-image img) {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

:deep(.n-image-preview-container) {
  max-width: 100%;
  max-height: 100%;
}

:deep(.n-image-preview) {
  max-width: 100%;
  max-height: 100%;
}

.pdf-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 0;
}

.pdf-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.pdf-container:hover .pdf-overlay {
  opacity: 1;
}

.preview-pdf {
  width: 100%;
  height: 100%;
  border: none;
  background: white;
}

.result-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

:deep(.result-card .n-card-header) {
  flex-shrink: 0;
}

:deep(.result-card .n-card__content) {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.upload-trigger {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
}

.empty-result {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
